import requests
import json
import time
from typing import List, Dict, Optional

class QuranTafsirProcessor:
    def __init__(self):
        self.base_url = "https://quranapi.pages.dev/api/tafsir"
        self.author_to_kitab_id = {
            "Ibn Kat<PERSON>": 2,
            "<PERSON><PERSON><PERSON>": 3,
            "Tazkirul Quran": 4
        }
        
    def get_surah_verse_counts(self, quran_arabic_path: str = "quran_arabic.json") -> Dict[int, int]:
        """Get the number of verses for each surah from quran_arabic.json"""
        with open(quran_arabic_path, 'r', encoding='utf-8') as f:
            verses = json.load(f)
        
        surah_counts = {}
        for verse in verses:
            surah_num = verse["surah_number"]
            ayah_num = verse["ayah_number"]
            if surah_num not in surah_counts:
                surah_counts[surah_num] = ayah_num
            else:
                surah_counts[surah_num] = max(surah_counts[surah_num], ayah_num)
        
        return surah_counts
    
    def fetch_tafsir(self, surah_no: int, ayah_no: int) -> Optional[Dict]:
        """Fetch tafsir data from the API"""
        url = f"{self.base_url}/{surah_no}_{ayah_no}.json"
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching tafsir for {surah_no}:{ayah_no} - {e}")
            return None
    
    def extract_verse_range(self, group_verse: str) -> Optional[str]:
        """Extract verse range from groupVerse text"""
        if not group_verse:
            return None
        
        # Look for patterns like "19:56 to 19:57" or "19:54 to 19:57"
        import re
        pattern = r'(\d+:\d+)\s+to\s+(\d+:\d+)'
        match = re.search(pattern, group_verse)
        
        if match:
            start_verse = match.group(1)
            end_verse = match.group(2)
            
            # Extract just the ayah numbers to create range format
            start_parts = start_verse.split(':')
            end_parts = end_verse.split(':')
            
            if len(start_parts) == 2 and len(end_parts) == 2:
                surah = start_parts[0]
                start_ayah = start_parts[1]
                end_ayah = end_parts[1]
                
                if start_ayah == end_ayah:
                    return f"{surah}:{start_ayah}"
                else:
                    return f"{surah}:{start_ayah}-{end_ayah}"
        
        return None
    
    def format_tafsir_response(self, api_response: Dict, original_surah: int, original_ayah: int) -> List[Dict]:
        """Format the API response into the required JSON structure"""
        if not api_response or 'tafsirs' not in api_response:
            return []
        
        formatted_tafsirs = []
        
        for tafsir in api_response['tafsirs']:
            author = tafsir.get('author', '')
            content = tafsir.get('content', '')
            group_verse = tafsir.get('groupVerse')
            
            # Get kitab_id based on author
            kitab_id = self.author_to_kitab_id.get(author)
            if not kitab_id:
                continue  # Skip unknown authors
            
            # Determine verse_keys
            if group_verse:
                verse_keys = self.extract_verse_range(group_verse)
                if not verse_keys:
                    # Fallback to original verse if parsing fails
                    verse_keys = f"{original_surah}:{original_ayah}"
            else:
                # Use original surah and ayah
                verse_keys = f"{original_surah}:{original_ayah}"
            
            formatted_tafsir = {
                "kitab_id": kitab_id,
                "verse_keys": verse_keys,
                "text": content,
                "language": "english"
            }
            
            formatted_tafsirs.append(formatted_tafsir)
        
        return formatted_tafsirs
    
    def process_surah_range(self, start_surah: int, end_surah: int, delay: float = 0.5) -> List[Dict]:
        """Process a range of surahs and return all formatted tafsirs"""
        surah_counts = self.get_surah_verse_counts()
        all_tafsirs = []
        
        for surah_no in range(start_surah, end_surah + 1):
            if surah_no not in surah_counts:
                print(f"Surah {surah_no} not found in quran_arabic.json")
                continue
            
            verse_count = surah_counts[surah_no]
            print(f"Processing Surah {surah_no} with {verse_count} verses...")
            
            for ayah_no in range(1, verse_count + 1):
                print(f"  Fetching tafsir for {surah_no}:{ayah_no}")
                
                # Fetch tafsir from API
                api_response = self.fetch_tafsir(surah_no, ayah_no)
                
                if api_response:
                    # Format the response
                    formatted_tafsirs = self.format_tafsir_response(api_response, surah_no, ayah_no)
                    all_tafsirs.extend(formatted_tafsirs)
                    
                    print(f"    Added {len(formatted_tafsirs)} tafsir entries")
                else:
                    print(f"    Failed to fetch tafsir for {surah_no}:{ayah_no}")
                
                # Add delay to be respectful to the API
                time.sleep(delay)
        
        return all_tafsirs

def main():
    """Test the processor with surahs 110-114"""
    processor = QuranTafsirProcessor()
    
    print("Testing Quran Tafsir Processor with Surahs 110-114...")
    print("=" * 60)
    
    # Process test range
    tafsirs = processor.process_surah_range(110, 114)
    
    print(f"\nTotal tafsir entries processed: {len(tafsirs)}")
    print("\nSample entries:")
    print("=" * 60)
    
    # Print first few entries as examples
    for i, tafsir in enumerate(tafsirs[:5]):
        print(f"\nEntry {i+1}:")
        print(json.dumps(tafsir, indent=2, ensure_ascii=False))
    
    if len(tafsirs) > 5:
        print(f"\n... and {len(tafsirs) - 5} more entries")
    
    # Save to file for inspection
    output_file = "test_tafsirs_110_114.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(tafsirs, f, indent=2, ensure_ascii=False)
    
    print(f"\nAll tafsir entries saved to: {output_file}")

if __name__ == "__main__":
    main()
