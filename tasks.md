﻿# Format Quran Tafsir form API
get quran tafsir from this api:
https://quranapi.pages.dev/api/tafsir/<surahNo>_<ayahNo>.json
where you need to replace the surahNo and ayahNo with actual number

# Format the response into formatted JSON
after get the response, you need to format the response into json format into something like this:

{
"kitab_id": 2,
"verse_keys": "2:15",
"text": "......",
"language": "english",
}

the original response is something like this: 

{
    "surahName": "Mary<PERSON>",
    "surahNo": 19,
    "ayahNo": 56,
    "tafsirs": [
        {
            "author": "<PERSON>",
            "groupVerse": "You are reading a tafsir for the group of verses 19:56 to 19:57",
            "content": "## Mentioning <PERSON><PERSON><PERSON>\n\n<PERSON><PERSON> complimented <PERSON><PERSON><PERSON> for being a truthful Prophet and He mentioned that he raised him to a high station. It has previously been mentioned that in the Sahih it is recorded that the Messenger of Allah ﷺ passed by <PERSON><PERSON><PERSON> on the night of the Isra (Night Journey) and he (<PERSON><PERSON><PERSON>) was in the fourth heaven. <PERSON><PERSON><PERSON> reported from Mansur that <PERSON><PERSON><PERSON> said,\n\nوَرَفَعْنَاهُ مَكَاناً عَلِيّاً\n\n(And We raised him to a high station.) \"This means the fourth heaven.\" <PERSON><PERSON><PERSON> and others said concer- ning <PERSON>'s statement,\n\nوَرَفَعْنَاهُ مَكَاناً عَلِيّاً\n\n(And We raised him to a high station.) \"This means Paradise.\""
        },
        {
            "author": "Maarif Ul Quran",
            "groupVerse": null,
            "content": "...."
        },
        {
            "author": "Tazkirul Quran",
            "groupVerse": "You are reading a tafsir for the group of verses 19:54 to 19:57",
            "content": "Ishmael was Abraham’s son. Idris was a prophet who was probably born before Noah. Here, two special virtues of these prophets are described: first, their truthfulness and second, their diligence in exhorting people to pray to God and offer alms, or zakat, in order to give God’s other subjects their rightful dues. God says here that these qualities have put the above prophets among His favoured servants, whom He shall raise on high. The persons God selected for prophethood possessed these qualities to the fullest extent. However, the faithful in general are also expected to evince these qualities, and they too, in varying degrees, will forever partake of the divinely ordained fruits born of these qualities."
        }
    ]
}

where if the author is Ibn Kathir, kitab_id = 2,
for Maarif Ul Quran, kitab_id = 3,
and Tazkirul Quran, kitab_id = 4,

then, for the verse_keys, you need to take note on the groupVerse, and also the surahNo and ayahNo.

meaning, 1 ayah could be in a group of tafsir, like as the example, even I request ayahNo 56, it gives me the tafsir for ayah 56-57 for ibn kathir, and can be also ayah 54-57 for tazkirul quran. if its null, then that mean the tafsir only for the ayah itself.

so for this, the result should be something like:

For ibn kathir:
{
"kitab_id": 2,
"verse_keys": "19:56-57",
"text": "..( ibn kathir content ) ....",
"language": "english",
}

For maarif quran:
{
"kitab_id": 3,
"verse_keys": "19:56",  ( take from the original surahNo and ayahNo )
"text": "..( content ) ....",
"language": "english",
}

For Tazkirul Quran:
{
"kitab_id": 4,
"verse_keys": "19:54-57",
"text": "..( content ) ....",
"language": "english",
}


# Looping

Make sure to loop all of this by the number of surah and verses for each surah.
like surah no 1 will has 7 verses, and so on.. 


# Testing
For now, only do it for the surah no. 110 to surah no. 114  just for testing and print it out


