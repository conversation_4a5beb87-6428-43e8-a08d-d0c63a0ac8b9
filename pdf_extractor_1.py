import PyPDF2
from pdfminer.high_level import extract_text
import pytesseract
from pdf2image import convert_from_path
import re
import json
from datetime import datetime
import os
from supabase import create_client
from dotenv import load_dotenv
load_dotenv()

url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_KEY")
supabase = create_client(url, key)

def extract_text_with_pypdf2(pdf_path, page_num=0):
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            if page_num >= len(reader.pages):
                return f"Error: PDF has only {len(reader.pages)} pages"
            return reader.pages[page_num].extract_text()
    except Exception as e:
        return f"Error: {e}"
    


def extract_text_with_pdfminer(pdf_path, page_range=None):
    try:
        if page_range is None:
            # Extract all pages
            return extract_text(pdf_path)
        elif isinstance(page_range, int):
            # Extract single page
            return extract_text(pdf_path, page_numbers={page_range})
        elif isinstance(page_range, (tuple, list)) and len(page_range) == 2:
            # Extract range of pages
            start_page, end_page = page_range
            page_numbers = set(range(start_page, end_page + 1))
            return extract_text(pdf_path, page_numbers=page_numbers)
        else:
            return "Error: page_range must be an int, tuple of (start, end), or None"
    except Exception as e:
        return f"Error: {e}"

def extract_text_with_ocr(pdf_path, page_num=None):
    poppler_path = r'E:\Program Files\poppler-24.08.0\Library\bin'
    pytesseract.pytesseract.tesseract_cmd = r'E:\Program Files\Tesseract-OCR\tesseract.exe'
    try:
        if page_num is not None:
            # Convert only the specified page (first_page and last_page are 1-indexed)
            images = convert_from_path(pdf_path, first_page=page_num+1, last_page=page_num+1, poppler_path=poppler_path)
        else:
            # Convert all pages
            images = convert_from_path(pdf_path, poppler_path=poppler_path)
            
        text = ""
        for image in images:
            text += pytesseract.image_to_string(image)
        return text
    except Exception as e:
        return f"Error: {e}"

def extract_verses_to_json(text):
    current_time = datetime.now().isoformat()

    # Remove all backslashes from the text
    text = text.replace('\\', '')

    # Updated pattern to match both formats:
    # [2:25] or [al-Baqarah, 2:25] or [Surah Name, 2:25-30]
    verse_pattern = r'\[(?:[^,\]]+,\s*)?(\d+:\d+(?:-\d+)?)\]'

    parts = re.split(verse_pattern, text)

    results = []
    i = 1

    while i < len(parts) - 1:
        verse_key = parts[i]
        content = parts[i + 1].strip()

        content = re.sub(r'^\s*\([^)]+\)\s*', '', content)

        # Remove page numbers (standalone digits)
        content = re.sub(r'^\s*\d+\s*$', '', content)
        content = re.sub(r'\s+\d+\s*$', '', content)

        # Remove newlines and normalize whitespace
        content = re.sub(r'\n', ' ', content)
        content = re.sub(r'\s+', ' ', content).strip()

        # ULTRA AGGRESSIVE backslash and escape sequence removal
        # Step 1: Remove ALL backslashes completely first
        content = content.replace('\\', '')

        # Step 2: Clean up the mess left behind by removing backslashes
        # After removing \, sequences like \n become n, \t becomes t, etc.
        # We need to clean these up contextually

        # Clean up common escape sequence remnants
        content = re.sub(r'\bn\b', ' ', content)      # standalone 'n' (from \n) -> space
        content = re.sub(r'\bt\b', ' ', content)      # standalone 't' (from \t) -> space
        content = re.sub(r'\br\b', '', content)       # standalone 'r' (from \r) -> remove
        content = re.sub(r'\bf\b', '', content)       # standalone 'f' (from \f) -> remove
        content = re.sub(r'\bb\b', '', content)       # standalone 'b' (from \b) -> remove

        # Clean up Unicode sequence remnants (u2026, u201c, etc.)
        content = re.sub(r'u[0-9a-fA-F]{4}', '', content)  # Remove uXXXX sequences
        content = re.sub(r'x[0-9a-fA-F]{2}', '', content)  # Remove xXX sequences

        # Clean up quote issues - if we see quote followed by space and period, clean it up
        content = re.sub(r'"\s*\.', '.', content)     # " . -> .
        content = re.sub(r'"\s*,', ',', content)      # " , -> ,
        content = re.sub(r'"\s*;', ';', content)      # " ; -> ;
        content = re.sub(r'"\s*:', ':', content)      # " : -> :
        content = re.sub(r'"\s+and', ' and', content) # " and -> and
        content = re.sub(r'"\s+', ' ', content)       # " followed by spaces -> single space

        # Clean up multiple quotes and problematic quote patterns
        content = re.sub(r'"+', '"', content)         # Multiple quotes -> single quote
        content = re.sub(r'quotes"', 'quotes', content)  # Remove trailing quote after "quotes"

        # Replace common Unicode characters with their ASCII equivalents
        # content = content.replace(u'\u201c', '"').replace(u'\u201d', '"')  # Replace curly quotes
        content = content.replace(u'\u2018', "'").replace(u'\u2019', "'")  # Replace curly single quotes
        content = content.replace(u'\u2026', '...')  # Replace ellipsis
        content = content.replace(u'\u2014', '--')  # Replace em dash
        content = content.replace(u'\u2013', '-')   # Replace en dash
        content = content.replace(u'\u00a0', ' ')   # Replace non-breaking space

        # Clean up quotes and punctuation issues
        content = re.sub(r'"+', '"', content)  # Replace multiple quotes with single quote
        content = re.sub(r"'+", "'", content)  # Replace multiple apostrophes with single
        content = re.sub(r'\."+', '.', content)  # Replace ." with just .
        content = re.sub(r'\.\'', '.', content)  # Replace .' with just .
        content = re.sub(r'"\s*\.', '.', content)  # Replace " . with just .

        # Remove leading punctuation, dots, and whitespace - ensure text starts with a letter
        content = re.sub(r'^[\s\.\,\;\:\!\?\-\"\'\(\)]+', '', content)

        # Normalize multiple spaces and clean up
        content = re.sub(r'\s+', ' ', content).strip()

        # Skip empty content or very short content (less than 10 characters)
        if not content or len(content.strip()) < 10:
            i += 2
            continue

        # Create the verse entry
        verse_entry = {
            "verse_keys": verse_key,
            "text": content,
            "created_at": current_time,
            "updated_at": current_time
        }

        results.append(verse_entry)
        i += 2

    return json.dumps(results, indent=2, ensure_ascii=False)

# Examples of using the updated function:
# Single page (0-indexed)
# text = extract_text_with_pdfminer("asbab/asbab_alwahidi.pdf", page_range=15)

# Range of pages (0-indexed, inclusive)
# text = extract_text_with_pdfminer("asbab/asbab_alwahidi.pdf", page_range=(15, 20))

# All pages
# text = extract_text_with_pdfminer("asbab/asbab_alwahidi.pdf", page_range=None)

# Example usage:
text = extract_text_with_pdfminer("asbab/asbab_alwahidi.pdf", page_range=(15,185))
json_text = extract_verses_to_json(text)

# insert each verse entry from json_text into supabase with custom json
import json

verses = json.loads(json_text)
responses = []
for verse in verses:
    print(f"Inserted verse {verse['verse_keys']}")
    response = supabase.table("asbab").insert({
        "verse_keys": verse["verse_keys"],
        "text": verse["text"],
        "language": "english",
        "kitab_id": 1,
        "created_at": verse["created_at"],
        "updated_at": verse["updated_at"],
    }).execute()
    responses.append(response)






