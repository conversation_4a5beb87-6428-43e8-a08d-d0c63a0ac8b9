import os
import json
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_KEY")
supabase = create_client(url, key)

input_path = 'translations/ms.basmeih.txt'
language = 'malay'
name = '<PERSON><PERSON><PERSON><PERSON>'
translator = '<PERSON>'

translations = []

with open(input_path, 'r', encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        if not line:
            continue
        parts = line.split('|', 2)
        if len(parts) != 3:
            continue  # skip malformed lines
        surah, ayah, text = parts
        translation_obj = {
            "verse_keys": f"{surah}:{ayah}",
            "language": language,
            "name": name,
            "translator": translator,
            "text": text
        }
        translations.append(translation_obj)

# Insert each translation into the "translations" table
for translation in translations:
    response = supabase.table("translations").insert(translation).execute()
    print(f"Inserted {translation['verse_keys']}")

print("All translations inserted.")
