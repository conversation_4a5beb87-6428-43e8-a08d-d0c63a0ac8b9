import json

input_path = 'translations/quran-simple.txt'
# output_path = 'quran-simple.json'  # No longer needed

result = []

with open(input_path, 'r', encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        if not line:
            continue
        parts = line.split('|', 2)
        if len(parts) != 3:
            continue  # skip malformed lines
        surah, ayah, text = parts
        verse_obj = {
            "surah_number": int(surah),
            "ayah_number": int(ayah),
            "verse_keys": f"{surah}:{ayah}",
            "text_arabic": text
        }
        result.append(verse_obj)

import os
import json
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_KEY")
supabase = create_client(url, key)

# Read the JSON file
with open('quran_arabic.json', 'r', encoding='utf-8') as f:
    verses = json.load(f)

# Insert each verse into the "verses" table
for verse in verses:
    data = {
        "surah_number": verse["surah_number"],
        "ayah_number": verse["ayah_number"],
        "verse_keys": verse["verse_keys"],
        "text_arabic": verse["text_arabic"]
    }
    response = supabase.table("verses").insert(data).execute()
    print(f"Inserted {verse['verse_keys']}: {response}")

print("All verses inserted.")





